import { Hash, Tag } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import type { CreateBusinessLineModalProps } from "./use-create-modal";
import useCreateBusinessLineModal from "./use-create-modal";

export default function CreateBusinessLineModal({
	isOpen,
	setIsOpen,
	parentId,
}: CreateBusinessLineModalProps) {
	const { businessLine } = useService();
	const { form, handleClose, isPending } = useCreateBusinessLineModal({
		isOpen,
		setIsOpen,
		parentId,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">
					{parentId ? "Crear Sub-línea de Negocio" : "Crear Línea de Negocio"}
				</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la línea de negocio"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(businessLine.validateCode(value));
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la línea de negocio"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
