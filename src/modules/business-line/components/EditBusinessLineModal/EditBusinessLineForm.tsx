import { Hash, Tag } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import { AppRuntime } from "src/core/service/utils/runtimes";
import type { BusinessLine } from "../../service/model/business-line";
import useEditBusinessLineModal from "./use-edit-business-line-modal";

interface Props {
	businessLine: BusinessLine;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	parentId?: string;
}

export default function EditBusinessLineForm({ businessLine, setIsOpen, parentId }: Props) {
	const { businessLine: businessLineService } = useService();
	const { form, handleClose } = useEditBusinessLineModal({
		setIsOpen,
		businessLine,
		parentId,
	});

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<form.AppField
						name="name"
						children={({ FSTextField }) => (
							<FSTextField
								label="Nombre"
								placeholder="Nombre de la línea de negocio"
								prefixComponent={<Tag size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="code"
						validators={{
							onChangeAsyncDebounceMs: 500,
							onChangeAsync: async ({ value }) => {
								if (!value || value.trim() === "" || value === businessLine.code) {
									return undefined;
								}
								try {
									await AppRuntime.runPromise(
										businessLineService.validateCode(value),
									);
									return undefined;
								} catch (e) {
									return [{ message: "El código ya existe" }];
								}
							},
						}}
						children={({ FSTextField }) => (
							<FSTextField
								label="Código"
								placeholder="Código de la línea de negocio"
								prefixComponent={<Hash size={16} />}
							/>
						)}
					/>
				</fieldset>
				<div className="modal-action">
					<form.SubscribeButton
						label="Actualizar"
						className="btn btn-primary"
					/>
				</div>
			</form.AppForm>
		</form>
	);
}
