import { Schema } from "effect";

export const BusinessLine = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	parentId: Schema.NullOr(Schema.String),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type BusinessLine = typeof BusinessLine.Type;

export const BusinessLineCreate = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	parentId: Schema.optional(Schema.String),
});
export type BusinessLineCreate = typeof BusinessLineCreate.Type;

export const BusinessLineUpdate = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	parentId: Schema.optional(Schema.String),
});
export type BusinessLineUpdate = typeof BusinessLineUpdate.Type;

export const BusinessLineWithSublines = Schema.Struct({
	businessLine: BusinessLine,
	sublines: Schema.Array(BusinessLine),
	parent: Schema.optional(BusinessLine),
});
export type BusinessLineWithSublines = typeof BusinessLineWithSublines.Type;
